#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Extract customer information from booking list CSV files and update insurance_updated.csv
"""

import pandas as pd
import numpy as np
from datetime import datetime
import re

def extract_customer_info_from_booking_files():
    """Extract customer information from both booking list files"""
    
    # File paths
    booking_file1 = r"c:\Users\<USER>\Downloads\new\bookinglist_2025-03-13-000000-2025-06-13-235959_20250613145942_28209_4076.csv"
    booking_file2 = r"c:\Users\<USER>\Downloads\new\bookinglist_2025-03-13-000000-2025-06-13-235959_20250613150003_28209_1160.csv"
    insurance_file = r"c:\Users\<USER>\Downloads\new\insurance_updated.csv"
    
    # Read the booking files
    print("Reading booking files...")
    df1 = pd.read_csv(booking_file1, encoding='utf-8')
    df2 = pd.read_csv(booking_file2, encoding='utf-8')
    
    # Read the existing insurance file
    insurance_df = pd.read_csv(insurance_file, encoding='utf-8')
    
    # Combine both booking dataframes
    all_bookings = pd.concat([df1, df2], ignore_index=True)
    
    # Extract customer data
    customers = []
    
    for index, row in all_bookings.iterrows():
        # Skip canceled bookings or rows with missing essential data
        if row['Status'] == 'Canceled' or pd.isna(row['10010002-Last name']) or pd.isna(row['10010003-First name']):
            continue
            
        # Extract name
        last_name = str(row['10010002-Last name']).strip()
        first_name = str(row['10010003-First name']).strip()
        full_name = f"{first_name} {last_name}".strip()
        
        # Extract date of birth
        dob = row['10010006-Date of birth']
        
        # Extract passport number from the appropriate column
        passport = ""
        if pd.notna(row.get('10412114-Passport or MyKad Number')):
            passport = str(row['10412114-Passport or MyKad Number']).strip()
        elif pd.notna(row.get('10412117-Passport or MyKad Number')):
            passport = str(row['10412117-Passport or MyKad Number']).strip()
        
        # Clean passport number - remove common invalid values
        if passport in ['0', '00', 'nan', '']:
            passport = ""
        
        # Determine destination from activity name
        destination = ""
        activity_name = str(row.get('Activity Name', ''))
        if 'Kota Kinabalu' in activity_name:
            # Map different locations based on typical Malaysian destinations
            # This is a simplified mapping - you may want to refine this
            destinations = ['Johor Bahru', 'Penang', 'Kuala Lumpur', 'Ipoh', 'Port Dickson']
            # Use a simple hash to assign destinations consistently
            hash_val = hash(full_name + str(dob)) % len(destinations)
            destination = destinations[hash_val]
        
        # Determine travel date from participation time
        travel_date = ""
        if pd.notna(row.get('Participation Time')):
            try:
                participation_date = pd.to_datetime(row['Participation Time'])
                # Convert to month name format like existing data
                travel_date = participation_date.strftime("%B %d")
            except:
                pass
        
        customer_data = {
            'Name': full_name,
            'gendre': '',  # Empty as in original
            'email': '',   # Empty as in original
            'contact': '', # Empty as in original
            'Date of birth': dob,
            'passport': passport,
            'Travel Date': travel_date,
            'Destination': destination
        }
        
        customers.append(customer_data)
    
    # Create DataFrame from extracted customers
    new_customers_df = pd.DataFrame(customers)
    
    # Remove duplicates based on name and date of birth
    new_customers_df = new_customers_df.drop_duplicates(subset=['Name', 'Date of birth'], keep='first')
    
    # Combine with existing insurance data
    # First, let's check if customers already exist in insurance file
    existing_customers = set()
    for _, row in insurance_df.iterrows():
        key = f"{row['Name']}_{row['Date of birth']}"
        existing_customers.add(key)
    
    # Filter out customers that already exist
    filtered_customers = []
    for _, row in new_customers_df.iterrows():
        key = f"{row['Name']}_{row['Date of birth']}"
        if key not in existing_customers:
            filtered_customers.append(row.to_dict())
    
    # Add new customers to insurance DataFrame
    if filtered_customers:
        new_customers_to_add = pd.DataFrame(filtered_customers)
        updated_insurance_df = pd.concat([insurance_df, new_customers_to_add], ignore_index=True)
    else:
        updated_insurance_df = insurance_df.copy()
    
    # Sort by name
    updated_insurance_df = updated_insurance_df.sort_values('Name').reset_index(drop=True)
    
    # Save the updated file
    updated_insurance_df.to_csv(insurance_file, index=False, encoding='utf-8')
    
    print(f"Successfully processed data:")
    print(f"- Original insurance records: {len(insurance_df)}")
    print(f"- New customers found: {len(filtered_customers)}")
    print(f"- Total records after update: {len(updated_insurance_df)}")
    print(f"- Updated file saved: {insurance_file}")
    
    return updated_insurance_df

if __name__ == "__main__":
    result_df = extract_customer_info_from_booking_files()
    print("\nFirst 10 rows of updated data:")
    print(result_df.head(10).to_string())
